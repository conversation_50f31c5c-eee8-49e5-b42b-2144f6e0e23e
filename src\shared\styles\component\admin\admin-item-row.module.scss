@import "../../vars/_vars";

.wrapper {
  display: flex;
  justify-content: center;
  animation: fadeIn 0.2s ease-in-out forwards;
  animation-delay: 0.1s;
  opacity: 0;
  gap: 8px;

  &__no_anim {
    animation: none;
    opacity: 1;
  }

  &__small {
    align-items: center;
    max-height: 42px;
    padding: 0;
    color: black;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: transparent;
    gap: 0;

    .container,
    .action {
      height: 100%;
      margin: 0;
      border: none;
      background: none;

      &:hover {
        border: none !important;

      }
    }

    .actions,
    .action {
      max-height: 40px;
    }

    &>.trash {
      margin-left: -12px;
      background-color: transparent;
    }

  }
}


@keyframes fadeIn {

  0% {
    opacity: 0;
    transform: scale(0.95) translateY(-15px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0px);
  }
}


.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 42px;
  //margin-top: 2px;
  padding: 0 16px;
  cursor: pointer;
  transition: background-color, color, border, transform;
  transition-duration: 0.15s;
  color: black;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;
  gap: 8px;

  &:hover:not(&__no_hover) {
    transform: scale(1.01);
    color: $paperbox-blue;
    border: 1px solid #d0d0d0;
  }

  &__no_hover {
    cursor: default;
    transition: background-color, color, border;
    transform: none;
  }

}


.title {
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-right: auto;
  margin-left: 0;
  gap: 8px;
}

.usage_info {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 16px;
}

.usage_badge {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 20px;
  padding: 0 6px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  background: $paperbox-blue;
  border-radius: 10px;
  cursor: help;
}

.usage_badge_unused {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 20px;
  padding: 0 6px;
  font-size: 11px;
  font-weight: 600;
  color: #898B99;
  background: #F5F5F5;
  border-radius: 10px;
  cursor: help;
}

// Group styles - improved alignment
.group {
  width: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;

  &:not(:last-child) {
    border-bottom: 1px solid #EEEEEE;
  }

  margin-bottom: 10px;
  padding-bottom: 20px;

  &_header {
    margin-top: 10px;
    font-size: 13px;
    font-weight: 500;
    padding-left: 2px;
    display: flex;
    align-items: center;
    margin-bottom: -10px;
  }

}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 31px;
  margin-left: -16px;
}


.color_dot {
  width: 12px;
  height: 6px;
  border-radius: 8px;
}


.actions {
  @include flex-center;
  height: 42px;
  gap: 8px;

}


.action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: center;
  width: 42px;
  height: 42px;
  transition: box-shadow 0.1s ease-in-out;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  position: relative;

  background: white;

  &:disabled {
    opacity: 0.5;
  }

  &__long {
    width: auto;
  }

  svg {
    width: auto;
    height: 14px;
    cursor: pointer;
    color: #969FAD;
  }

  &:hover:not(&:disabled) svg {
    color: $paperbox-blue;
  }

  &.trash {
    svg {
      height: 15px;
    }

    &:hover:not(&:disabled) svg {
      color: $error;
    }
  }

  input {
    border: none;
    outline: none;
    background: none;
    width: 100%;
    padding: 10px;
    font-size: 13px;
  }

  .button {
    width: auto;
    aspect-ratio: 1;
    height: 100%;
    border: none;
    border-radius: 0;
    position: absolute;
    right: 0px;

    svg {
      margin-top: 2px;

      width: 16px;
      height: 16px;
    }
  }

  .loading_container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

}